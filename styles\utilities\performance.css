/* ===== OTIMIZAÇÕES DE PERFORMANCE ===== */

/* Otimizações gerais de performance */
* {
    /* <PERSON><PERSON><PERSON><PERSON> rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Otimizar animações para 60fps */
@media (prefers-reduced-motion: no-preference) {
    .link-button,
    .link-item,
    .config-button,
    .config-modal-content {
        will-change: transform;
    }
}

/* Reduzir animações para usuários que preferem menos movimento */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Otimizações para dispositivos com baixa performance */
@media (max-width: 480px) {
    /* Reduzir blur effects em mobile */
    .config-modal {
        backdrop-filter: blur(5px);
    }
    
    .link-button {
        backdrop-filter: blur(3px);
    }
}

/* Otimizações para conexões lentas */
@media (prefers-reduced-data: reduce) {
    /* Desabilitar efeitos pesados */
    .link-button::before,
    .link-button::after,
    .link-item::before,
    .link-item::after,
    .config-button::before,
    .config-modal-content::before,
    .config-modal-content::after {
        display: none !important;
    }
    
    /* Simplificar animações */
    * {
        animation: none !important;
        transition: none !important;
    }
}
