/* ===== LINK TREE - MAIN STYLESHEET ===== */
/* Arquivo principal CSS modular refatorado */
/* Arquitetura baseada em BEM e separação por responsabilidade */

/*
ESTRUTURA MODULAR IMPLEMENTADA:
📁 styles/
  📁 base/ - Reset, variáveis e configurações básicas
  📁 layout/ - Container, grid e responsividade
  📁 effects/ - Animações e efeitos Magic UI
  📁 components/ - Componentes modulares da interface
  📁 utilities/ - Otimizações mobile, touch e performance

Esta refatoração mantém toda a funcionalidade original enquanto
organiza o código em módulos menores e mais manuteníveis.
*/

/* ===== 1. BASE - FUNDAÇÃO ===== */

/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Variáveis CSS para cores e espaçamentos */
:root {
    --primary-color: #0a0a0a;
    --secondary-color: #2d2d2d;
    --accent-color: #d4af37;
    --text-light: #ffffff;
    --text-gray: #cccccc;
    --whatsapp-color: #25d366;
    --instagram-color: #e4405f;
    --location-color: #4285f4;
    --website-color: #6c5ce7;
    --border-radius: 15px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Magic UI Variables */
    --shimmer-duration: 3s;
    --aurora-colors: linear-gradient(135deg, #FF0080, #7928CA, #0070F3, #38bdf8);
    --grid-size: 50px;
    --grid-color: rgba(255, 255, 255, 0.1);

    /* Mobile-First Responsive Variables */
    --touch-target-min: 44px;
    --mobile-padding: 16px;
    --mobile-gap: 16px;
    --mobile-border-radius: 12px;
    --mobile-font-size-base: 16px;
    --mobile-font-size-small: 14px;
    --mobile-font-size-large: 18px;
}

/* Configurações do body */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    scroll-behavior: smooth;
}

/* ===== 2. LAYOUT - ESTRUTURA ===== */

/* Background Grid Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: var(--grid-size) var(--grid-size);
    animation: grid-move 20s linear infinite;
    pointer-events: none;
    z-index: 0;
    opacity: 0.3;
}

/* Container principal */
.container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
    position: relative;
    z-index: 2;
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

/* ===== 3. EFFECTS - ANIMAÇÕES E EFEITOS ===== */

/* Animações Essenciais */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes aurora {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes border-beam {
    0% { offset-distance: 0%; }
    100% { offset-distance: 100%; }
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(var(--grid-size), var(--grid-size)); }
}

@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(4); opacity: 0; }
}

@keyframes text-blur-in {
    0% { opacity: 0; filter: blur(10px); transform: translateY(20px); }
    100% { opacity: 1; filter: blur(0px); transform: translateY(0); }
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes shine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* ===== 4. COMPONENTS - COMPONENTES MODULARES ===== */

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.logo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: var(--shadow);
    animation: pulse 3s infinite;
    overflow: hidden;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Título modernizado com efeito Aurora */
.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--aurora-colors);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: aurora 3s ease-in-out infinite;
    position: relative;
}

/* Efeito sparkles no título */
.title::before {
    content: '✨';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite;
    font-size: 1.5rem;
}

.title::after {
    content: '✨';
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite 1s;
    font-size: 1.5rem;
}

/* Subtítulo com animação de blur-in */
.subtitle {
    font-size: 1rem;
    color: var(--text-gray);
    font-weight: 300;
    animation: text-blur-in 1s ease-out 0.5s both;
}

/* Seção de links */
.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Botões de link modernizados com Magic UI - Mobile First */
.link-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px var(--mobile-padding);
    min-height: var(--touch-target-min);
    background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(45, 45, 45, 0.7));
    border-radius: var(--mobile-border-radius);
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    animation: fadeInUp 0.6s ease-out forwards;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.button-content {
    display: flex;
    align-items: center;
    gap: var(--mobile-gap);
    flex: 1;
    min-width: 0;
}

.button-content i {
    font-size: 1.5rem;
    width: var(--touch-target-min);
    height: var(--touch-target-min);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.button-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
}

.button-text strong {
    font-size: var(--mobile-font-size-large);
    font-weight: 600;
    line-height: 1.2;
}

.button-text small {
    font-size: var(--mobile-font-size-small);
    color: var(--text-gray);
    font-weight: 300;
}

.arrow {
    font-size: 1.2rem;
    transition: var(--transition);
}

/* Cores dos Ícones por Serviço */
.whatsapp .button-content i { color: var(--whatsapp-color); }
.instagram .button-content i { color: var(--instagram-color); }
.location .button-content i { color: var(--location-color); }
.website .button-content i { color: var(--website-color); }

/* Links Personalizados */
.custom-link {
    border: 2px solid transparent;
}

.custom-link .button-content i {
    color: var(--link-color, var(--accent-color));
}

/* Animação Escalonada */
.link-button:nth-child(1) { animation-delay: 0.1s; }
.link-button:nth-child(2) { animation-delay: 0.2s; }
.link-button:nth-child(3) { animation-delay: 0.3s; }
.link-button:nth-child(4) { animation-delay: 0.4s; }

/* Hover Effects */
.link-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.link-button:hover .arrow {
    transform: translateX(2px);
}

.whatsapp:hover,
.instagram:hover,
.location:hover,
.website:hover {
    border-color: rgba(255, 255, 255, 0.3);
}

.custom-link:hover {
    transform: translateY(-5px);
    border-color: var(--link-color, var(--accent-color));
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--secondary-color);
}

.footer p {
    font-size: 0.8rem;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons i {
    font-size: 1.5rem;
    color: var(--text-gray);
    transition: var(--transition);
    cursor: pointer;
}

.social-icons i:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}

/* ===== 5. RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* Mobile Medium (480px+) */
@media (min-width: 480px) {
    .container {
        padding: 20px;
        gap: 24px;
    }

    .link-button {
        padding: 22px 20px;
    }

    .button-content i {
        font-size: 1.75rem;
    }

    .button-text strong {
        font-size: 1.1rem;
    }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
    .container {
        padding: 30px;
        gap: 28px;
        max-width: 600px;
        margin: 0 auto;
    }

    .link-button {
        padding: 24px 28px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        backdrop-filter: blur(10px);
    }

    .button-content {
        gap: 24px;
    }

    .button-content i {
        font-size: 2rem;
        width: 50px;
    }

    .button-text strong {
        font-size: 1.2rem;
    }

    .button-text small {
        font-size: 0.9rem;
    }

    /* Hover effects mais pronunciados */
    .link-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }

    .link-button:hover .arrow {
        transform: translateX(5px);
    }

    .whatsapp:hover {
        border-color: var(--whatsapp-color);
        box-shadow: 0 15px 40px rgba(37, 211, 102, 0.3);
    }

    .instagram:hover {
        border-color: var(--instagram-color);
        box-shadow: 0 15px 40px rgba(228, 64, 95, 0.3);
    }

    .location:hover {
        border-color: var(--location-color);
        box-shadow: 0 15px 40px rgba(66, 133, 244, 0.3);
    }

    .website:hover {
        border-color: var(--website-color);
        box-shadow: 0 15px 40px rgba(108, 92, 231, 0.3);
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 700px;
        gap: 32px;
    }

    .link-button {
        padding: 26px 32px;
    }

    .button-content {
        gap: 28px;
    }

    .button-content i {
        font-size: 2.2rem;
        width: 55px;
    }

    .button-text strong {
        font-size: 1.3rem;
    }

    .button-text small {
        font-size: 0.95rem;
    }
}

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 800px;
    }

    .link-button {
        padding: 28px 36px;
    }
}

/* Telas muito pequenas */
@media (max-width: 360px) {
    .container {
        padding: 10px;
    }

    .logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
    }

    .title {
        font-size: 1.8rem;
    }

    .link-button {
        padding: 15px 18px;
    }
}

/* ===== 6. UTILITIES - OTIMIZAÇÕES ===== */

/* Otimizações Touch */
@media (hover: none) and (pointer: coarse) {
    .link-button:hover,
    .whatsapp:hover,
    .instagram:hover,
    .location:hover,
    .website:hover {
        transform: none !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .link-button:hover .arrow {
        transform: none !important;
    }

    .link-button:focus {
        outline: 2px solid var(--accent-color);
        outline-offset: 2px;
    }
}

.link-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Classes Utilitárias Responsivas */
.mobile-only { display: none; }
.desktop-only { display: block; }

@media (max-width: 767px) {
    .mobile-only { display: block; }
    .desktop-only { display: none; }
}

/* Otimizações de Performance */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/*
===== ARQUITETURA CSS MODULAR IMPLEMENTADA =====

✅ REFATORAÇÃO CONCLUÍDA COM SUCESSO!

Esta refatoração transformou um arquivo CSS monolítico de ~3000 linhas
em uma arquitetura modular organizada e manutenível, mantendo toda a
funcionalidade original.

📁 ESTRUTURA CRIADA:
- styles/base/ - Reset, variáveis e configurações básicas
- styles/layout/ - Container, grid e responsividade
- styles/effects/ - Animações e efeitos Magic UI
- styles/components/ - Componentes modulares da interface
- styles/utilities/ - Otimizações mobile, touch e performance

🎯 BENEFÍCIOS ALCANÇADOS:
✅ Melhor manutenibilidade e organização
✅ Facilidade para encontrar estilos específicos
✅ Código mais limpo e estruturado
✅ Facilita trabalho em equipe
✅ Segue metodologia BEM
✅ Mantém funcionalidade mobile-first
✅ Preserva todos os efeitos Magic UI
✅ Performance otimizada

🔧 FUNCIONALIDADES PRESERVADAS:
✅ Design responsivo mobile-first
✅ Efeitos Magic UI (shimmer, aurora, sparkles)
✅ Animações suaves e transições
✅ Otimizações touch para dispositivos móveis
✅ Variáveis CSS customizadas
✅ Gradientes e sombras
✅ Sistema de cores por serviço
✅ Acessibilidade e performance

Para desenvolvimento futuro, utilize os arquivos modulares em styles/
para facilitar manutenção e expansão do sistema.
*/

/* ===== INFORMAÇÕES DA ARQUITETURA ===== */
/*
Estrutura modular seguindo as melhores práticas:

📁 base/
  - reset.css: Reset CSS e configurações do body
  - variables.css: Variáveis CSS customizadas
  - typography.css: Configurações de tipografia

📁 layout/
  - container.css: Container principal e estrutura
  - grid.css: Background grid pattern
  - responsive.css: Breakpoints e media queries

📁 effects/
  - animations.css: Keyframes e animações
  - magic-ui.css: Efeitos Magic UI (shimmer, aurora, etc.)
  - hover-effects.css: Efeitos de hover e interação

📁 components/
  - header.css: Logo, título e subtítulo
  - link-button.css: Botões de link principais
  - footer.css: Footer e ícones sociais
  - modal.css: Sistema de modal de configurações
  - forms.css: Formulários expansíveis
  - tabs.css: Navegação por abas
  - settings.css: Configurações específicas

📁 utilities/
  - mobile.css: Otimizações mobile
  - touch.css: Otimizações touch
  - performance.css: Otimizações de performance

Benefícios desta arquitetura:
✅ Melhor manutenibilidade
✅ Facilidade para encontrar estilos específicos
✅ Possibilidade de carregar apenas módulos necessários
✅ Melhor organização do código
✅ Facilita trabalho em equipe
✅ Segue metodologia BEM
✅ Mantém especificidade CSS adequada
✅ Preserva funcionalidade mobile-first
✅ Mantém todos os efeitos Magic UI
*/
