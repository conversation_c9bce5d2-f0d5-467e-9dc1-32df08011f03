/* ===== COMPONENTE SETTINGS ===== */

.settings-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-item {
    background: var(--secondary-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.setting-info h4 {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.setting-info small {
    color: var(--text-gray);
    font-size: 0.85rem;
    line-height: 1.4;
}

.setting-control {
    flex-shrink: 0;
}

.form-select {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-select option {
    background: var(--primary-color);
    color: var(--text-light);
}

.form-input {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    transition: all 0.3s ease;
    width: 100%;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-input::placeholder {
    color: var(--text-gray);
    opacity: 0.7;
}

/* Mobile: Configurações em coluna */
@media (max-width: 767px) {
    .setting-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 12px;
    }

    .setting-control {
        align-self: center;
    }

    .form-select {
        min-width: 140px;
        min-height: 44px;
        font-size: 1rem;
    }

    .form-input {
        min-height: 44px;
        padding: 12px 16px;
        font-size: 1rem;
    }
}
