/* ===== RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* Mobile Small (320px+) - Base styles already defined */

/* Mobile Medium (480px+) */
@media (min-width: 480px) {
    .container {
        padding: 20px;
        gap: 24px;
    }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
    .container {
        padding: 30px;
        gap: 28px;
        max-width: 600px;
        margin: 0 auto;
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 700px;
        gap: 32px;
    }
}

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 800px;
    }
}

/* Telas muito pequenas */
@media (max-width: 360px) {
    .container {
        padding: 10px;
    }
}
