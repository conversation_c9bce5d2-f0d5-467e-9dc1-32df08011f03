/* ===== OTIMIZAÇÕES TOUCH ===== */

/* Otimizações Touch Específicas */
@media (hover: none) and (pointer: coarse) {
    /* Desabilitar hover effects em dispositivos touch */
    .link-button:hover,
    .whatsapp:hover,
    .instagram:hover,
    .location:hover,
    .website:hover {
        transform: none !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .link-button:hover .arrow {
        transform: none !important;
    }

    .link-button:focus {
        outline: 2px solid var(--accent-color);
        outline-offset: 2px;
    }
}

/* Feedback visual para touch */
.link-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Botões otimizados para touch */
@media (max-width: 767px) {
    /* Controles dos links */
    .control-btn {
        min-width: 44px;
        min-height: 44px;
        font-size: 1.1rem;
        border-radius: 8px;
    }

    /* Toggle switches maiores */
    .toggle-switch {
        width: 60px;
        height: 32px;
        border-radius: 32px;
    }

    .toggle-switch::after {
        width: 28px;
        height: 28px;
        top: 2px;
        left: 2px;
    }

    .toggle-switch.active::after {
        transform: translateX(28px);
    }

    /* Cards de links com melhor espaçamento */
    .link-item {
        padding: 20px 16px;
        margin-bottom: 12px;
        border-radius: 16px;
    }

    .link-item-header {
        gap: 22px;
    }

    /* Telas médias: Ícone com tamanho intermediário */
    .link-preview {
        width: 50px;
        height: 50px;
        font-size: 1.45rem;
        border-radius: 15px;
    }

    .link-preview::before {
        border-radius: 15px;
    }

    /* Telas médias: Gap intermediário entre nome e controles */
    .link-info {
        gap: 11px;
    }

    .link-controls {
        gap: 12px;
        flex-wrap: wrap;
    }
}
