/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Variáveis CSS para cores e espaçamentos */
:root {
    --primary-color: #0a0a0a;
    --secondary-color: #2d2d2d;
    --accent-color: #d4af37;
    --text-light: #ffffff;
    --text-gray: #cccccc;
    --whatsapp-color: #25d366;
    --instagram-color: #e4405f;
    --location-color: #4285f4;
    --website-color: #6c5ce7;
    --border-radius: 15px;
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Magic UI Variables */
    --shimmer-duration: 3s;
    --aurora-colors: linear-gradient(135deg, #FF0080, #7928CA, #0070F3, #38bdf8);
    --grid-size: 50px;
    --grid-color: rgba(255, 255, 255, 0.1);

    /* Mobile-First Responsive Variables */
    --touch-target-min: 44px;
    --mobile-padding: 16px;
    --mobile-gap: 16px;
    --mobile-border-radius: 12px;
    --mobile-font-size-base: 16px;
    --mobile-font-size-small: 14px;
    --mobile-font-size-large: 18px;

    /* Breakpoints (for reference in comments) */
    /* --mobile-xs: 320px */
    /* --mobile-sm: 480px */
    /* --tablet: 768px */
    /* --desktop: 1024px */
    /* --desktop-lg: 1200px */
}

/* Animações Essenciais */
@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes aurora {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes border-beam {
    0% { offset-distance: 0%; }
    100% { offset-distance: 100%; }
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(var(--grid-size), var(--grid-size)); }
}

@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(4); opacity: 0; }
}

@keyframes text-blur-in {
    0% { opacity: 0; filter: blur(10px); transform: translateY(20px); }
    100% { opacity: 1; filter: blur(0px); transform: translateY(0); }
}

@keyframes sparkle {
    0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

/* Configurações do body */
body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
    /* Estabilizar scroll global */
    scroll-behavior: smooth;
}

/* Background Grid Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: var(--grid-size) var(--grid-size);
    animation: grid-move 20s linear infinite;
    pointer-events: none;
    z-index: 0;
    opacity: 0.3;
}

/* Container principal */
.container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 30px;
    position: relative;
    z-index: 2;
    /* Prevenir overflow horizontal */
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 20px;
}

.logo {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    box-shadow: var(--shadow);
    animation: pulse 3s infinite;
    overflow: hidden;
}

.logo i {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Título modernizado com efeito Aurora */
.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: var(--aurora-colors);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: aurora 3s ease-in-out infinite;
    position: relative;
}

/* Efeito sparkles no título */
.title::before {
    content: '✨';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite;
    font-size: 1.5rem;
}

.title::after {
    content: '✨';
    position: absolute;
    right: -30px;
    top: 50%;
    transform: translateY(-50%);
    animation: sparkle 2s ease-in-out infinite 1s;
    font-size: 1.5rem;
}

/* Subtítulo com animação de blur-in */
.subtitle {
    font-size: 1rem;
    color: var(--text-gray);
    font-weight: 300;
    animation: text-blur-in 1s ease-out 0.5s both;
}

/* Seção de links */
.links-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Botões de link modernizados com Magic UI - Mobile First */
.link-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    /* Mobile: Padding otimizado para touch targets */
    padding: 20px var(--mobile-padding);
    min-height: var(--touch-target-min);
    background: linear-gradient(135deg, rgba(45, 45, 45, 0.9), rgba(45, 45, 45, 0.7));
    border-radius: var(--mobile-border-radius);
    text-decoration: none;
    color: var(--text-light);
    transition: var(--transition);
    /* Mobile: Sombra reduzida para performance */
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    /* Mobile: Backdrop filter reduzido */
    backdrop-filter: blur(5px);
    animation: fadeInUp 0.6s ease-out forwards;
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Shimmer effect - Desabilitado em mobile para performance */
.link-button::before {
    /* Mobile: Efeito desabilitado */
    display: none;
}

/* Border beam effect - Desabilitado em mobile para performance */
.link-button::after {
    /* Mobile: Efeito desabilitado */
    display: none;
}

/* Tablet+: Habilitar efeitos visuais */
@media (min-width: 768px) {
    .link-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        background-size: 200% 100%;
        background-position: -200% 0;
        animation: shimmer 3s ease-in-out infinite;
        z-index: 1;
        display: block;
    }

    .link-button::after {
        content: '';
        position: absolute;
        inset: 0;
        padding: 2px;
        background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
        border-radius: inherit;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        display: block;
    }
}

/* Hover Effects Consolidados */
.link-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

@media (min-width: 768px) {
    .link-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    }

    .link-button:hover::before {
        left: 100%;
    }

    .link-button:hover::after {
        opacity: 1;
        animation: border-beam 2s linear infinite;
    }
}

/* Mobile First: Button Content */
.button-content {
    display: flex;
    align-items: center;
    gap: var(--mobile-gap);
    flex: 1;
    min-width: 0; /* Permite text truncation se necessário */
}

.button-content i {
    /* Mobile: Ícone otimizado para touch */
    font-size: 1.5rem;
    width: var(--touch-target-min);
    height: var(--touch-target-min);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.button-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0; /* Permite text truncation */
}

.button-text strong {
    font-size: var(--mobile-font-size-large);
    font-weight: 600;
    line-height: 1.2;
}

.button-text small {
    font-size: var(--mobile-font-size-small);
    color: var(--text-gray);
    font-weight: 300;
}

.arrow {
    font-size: 1.2rem;
    transition: var(--transition);
}

/* Cores dos Ícones por Serviço */
.whatsapp .button-content i { color: var(--whatsapp-color); }
.instagram .button-content i { color: var(--instagram-color); }
.location .button-content i { color: var(--location-color); }
.website .button-content i { color: var(--website-color); }

/* Efeitos Hover Específicos */
.whatsapp:hover,
.instagram:hover,
.location:hover,
.website:hover {
    border-color: rgba(255, 255, 255, 0.3);
}

.link-button:hover .arrow {
    transform: translateX(2px);
}

@media (min-width: 768px) {
    .whatsapp:hover {
        border-color: var(--whatsapp-color);
        box-shadow: 0 15px 40px rgba(37, 211, 102, 0.3);
    }

    .instagram:hover {
        border-color: var(--instagram-color);
        box-shadow: 0 15px 40px rgba(228, 64, 95, 0.3);
    }

    .location:hover {
        border-color: var(--location-color);
        box-shadow: 0 15px 40px rgba(66, 133, 244, 0.3);
    }

    .website:hover {
        border-color: var(--website-color);
        box-shadow: 0 15px 40px rgba(108, 92, 231, 0.3);
    }

    .link-button:hover .arrow {
        transform: translateX(5px);
    }
}

/* Links Personalizados */
.custom-link {
    border: 2px solid transparent;
}

.custom-link:hover {
    transform: translateY(-5px);
    border-color: var(--link-color, var(--accent-color));
}

.custom-link .button-content i {
    color: var(--link-color, var(--accent-color));
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--secondary-color);
}

.footer p {
    font-size: 0.8rem;
    color: var(--text-gray);
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-icons i {
    font-size: 1.5rem;
    color: var(--text-gray);
    transition: var(--transition);
    cursor: pointer;
}

.social-icons i:hover {
    color: var(--accent-color);
    transform: scale(1.2);
}

/* Animações */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animação Escalonada */
.link-button:nth-child(1) { animation-delay: 0.1s; }
.link-button:nth-child(2) { animation-delay: 0.2s; }
.link-button:nth-child(3) { animation-delay: 0.3s; }
.link-button:nth-child(4) { animation-delay: 0.4s; }

/* ===== RESPONSIVE DESIGN - MOBILE FIRST ===== */

/* Mobile Small (320px+) - Base styles already defined above */

/* Mobile Medium (480px+) */
@media (min-width: 480px) {
    .container {
        padding: 20px;
        gap: 24px;
    }

    .link-button {
        padding: 22px 20px;
    }

    .button-content i {
        font-size: 1.75rem;
    }

    .button-text strong {
        font-size: 1.1rem;
    }
}

/* Tablet (768px+) */
@media (min-width: 768px) {
    .container {
        padding: 30px;
        gap: 28px;
        max-width: 600px;
        margin: 0 auto;
    }

    .link-button {
        padding: 24px 28px;
        border-radius: var(--border-radius);
        /* Tablet: Restaurar efeitos visuais */
        box-shadow: var(--shadow);
        backdrop-filter: blur(10px);
    }

    .button-content {
        gap: 24px;
    }

    .button-content i {
        font-size: 2rem;
        width: 50px;
    }

    .button-text strong {
        font-size: 1.2rem;
    }

    .button-text small {
        font-size: 0.9rem;
    }
}

/* Desktop (1024px+) */
@media (min-width: 1024px) {
    .container {
        max-width: 700px;
        gap: 32px;
    }

    .link-button {
        padding: 26px 32px;
        /* Desktop: Hover effects mais pronunciados */
    }



    .button-content {
        gap: 28px;
    }

    .button-content i {
        font-size: 2.2rem;
        width: 55px;
    }

    .button-text strong {
        font-size: 1.3rem;
    }

    .button-text small {
        font-size: 0.95rem;
    }
}

/* Desktop Large (1200px+) */
@media (min-width: 1200px) {
    .container {
        max-width: 800px;
    }

    .link-button {
        padding: 28px 36px;
    }
}

/* ===== OTIMIZAÇÕES TOUCH ESPECÍFICAS ===== */

/* Otimizações Touch */
@media (hover: none) and (pointer: coarse) {
    .link-button:hover,
    .whatsapp:hover,
    .instagram:hover,
    .location:hover,
    .website:hover {
        transform: none !important;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .link-button:hover .arrow {
        transform: none !important;
    }

    .link-button:focus {
        outline: 2px solid var(--accent-color);
        outline-offset: 2px;
    }
}

.link-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Otimizações de Performance */
.mobile-device .link-button::before,
.mobile-device .link-button::after,
.mobile-device .link-item::before,
.mobile-device .link-item::after,
.mobile-device .floating-particle {
    display: none !important;
}

.low-performance *,
.reduce-animations * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
    animation-iteration-count: 1 !important;
}

.low-performance .link-button:hover,
.low-performance .link-item:hover,
.reduce-animations .link-button:hover,
.reduce-animations .link-item:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.low-performance .config-modal-content {
    backdrop-filter: none !important;
    background: var(--primary-color) !important;
}

.reduce-animations .link-button::before,
.reduce-animations .link-button::after,
.reduce-animations .link-item::before,
.reduce-animations .link-item::after {
    animation: none !important;
}

@media (max-width: 360px) {
    .container {
        padding: 10px;
    }

    .logo {
        width: 90px;
        height: 90px;
        margin: 0 auto 15px;
    }

    .title {
        font-size: 1.8rem;
    }

    .link-button {
        padding: 15px 18px;
    }
}

/* ===== SISTEMA DE CONFIGURAÇÕES ===== */

/* Botão de Configurações com Magic UI */
.config-button {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    border: 2px solid transparent;
    border-radius: 50%;
    color: var(--text-light);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* Shimmer effect para o botão de configurações */
.config-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(212, 175, 55, 0.4),
        transparent
    );
    background-size: 200% 100%;
    background-position: -200% 0;
    animation: shimmer 3s ease-in-out infinite;
    z-index: 1;
}

.config-button:hover::before {
    background-position: 200% 0;
}

.config-button:hover {
    background: var(--accent-color);
    color: var(--primary-color);
    transform: rotate(90deg) scale(1.1);
    border-color: var(--accent-color);
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

.config-button i {
    position: relative;
    z-index: 2;
}

/* Modal de Configurações com Magic UI */
.config-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 2000;
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    padding: 20px;
}

.config-modal.active {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    animation: magicModalFadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Magic Card effect para o modal */
.config-modal-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(212, 175, 55, 0.1);
    animation: magicModalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 20px;
    position: relative;
    border: 1px solid transparent;
    background-clip: padding-box;
    /* Prevenir mudanças bruscas de altura */
    contain: layout style paint;
    /* Estabilizar dimensões */
    box-sizing: border-box;
    /* Estrutura flexível para controle de altura */
    display: flex;
    flex-direction: column;
}

/* Neon gradient border effect */
.config-modal-content::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg,
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: neonBorderRotate 3s linear infinite;
}

/* Spotlight effect que segue o mouse */
.config-modal-content::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(
        600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(212, 175, 55, 0.1),
        transparent 40%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.config-modal-content:hover::after {
    opacity: 1;
}

/* Header do Modal com Magic UI */
.config-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    position: relative;
    overflow: hidden;
}

/* Animated gradient text para o título */
.config-header h2 {
    background: linear-gradient(
        45deg,
        var(--accent-color),
        #f1c40f,
        var(--accent-color),
        #ffd700
    );
    background-size: 300% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Shimmer effect para o ícone do título */
.config-header h2 i {
    background: var(--accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
}

.config-close {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-close:hover {
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.1);
}

/* Corpo do Modal */
.config-body {
    padding: 30px;
    flex: 1; /* CORREÇÃO: Usar flex para ocupar espaço disponível */
    overflow: visible; /* FIX: Permite que o dropdown apareça */
    /* Estabilizar scroll */
    scroll-behavior: smooth;
    contain: layout style;
    /* Altura mínima para evitar colapso */
    min-height: 400px;
    /* Estrutura flexível */
    display: flex;
    flex-direction: column;
}

.config-section {
    margin-bottom: 40px;
    /* Estabilizar seções */
    contain: layout style;
}

.config-section h3 {
    color: var(--accent-color);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--secondary-color);
}

/* Lista de Links com Magic UI */
.links-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    /* Altura mínima para evitar colapso */
    min-height: 200px;
    contain: layout style;
}

/* Magic Cards para os itens de links - Mobile First */
.link-item {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #1f1f1f 100%);
    border-radius: var(--mobile-border-radius);
    /* Mobile: Padding otimizado para touch */
    padding: var(--mobile-padding);
    min-height: var(--touch-target-min);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile: Efeitos desabilitados para performance */
.link-item::before {
    display: none;
}

.link-item::after {
    display: none;
}

/* Tablet+: Habilitar efeitos Magic UI */
@media (min-width: 768px) {
    /* Border beam effect para os cards */
    .link-item::before {
        content: '';
        position: absolute;
        inset: 0;
        padding: 1px;
        background: linear-gradient(90deg,
            transparent,
            var(--accent-color),
            transparent
        );
        border-radius: inherit;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        opacity: 0;
        transition: opacity 0.3s ease;
        display: block;
    }

    /* Spotlight effect nos cards */
    .link-item::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: radial-gradient(
            400px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
            rgba(212, 175, 55, 0.08),
            transparent 40%
        );
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        display: block;
    }
}

/* Mobile: Hover simplificado */
.link-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

/* Tablet+: Hover effects completos */
@media (min-width: 768px) {
    .link-item:hover {
        transform: translateY(-2px);
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(212, 175, 55, 0.2);
    }

    .link-item:hover::before {
        opacity: 1;
        animation: border-beam 2s linear infinite;
    }

    .link-item:hover::after {
        opacity: 1;
    }
}

/* Mobile First: Link Item Header - Layout como na imagem */
.link-item-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 0;
}

/* Ícone: Posicionado na segunda linha com destaque visual */
.link-preview {
    width: var(--touch-target-min);
    height: var(--touch-target-min);
    border-radius: 12px; /* Bordas mais arredondadas */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem; /* Ícone ligeiramente maior */
    color: white;
    flex-shrink: 0;
    /* Destaque visual com sombra sutil */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    /* Background overlay para melhor contraste */
    position: relative;
    /* Transição suave para efeitos hover */
    transition: all var(--transition);
}

/* Overlay para melhorar contraste do ícone */
.link-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    pointer-events: none;
    transition: var(--transition);
}

/* Efeito hover sutil para o ícone */
.link-item:hover .link-preview {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.link-item:hover .link-preview::before {
    background: rgba(0, 0, 0, 0.05);
}

/* Container do conteúdo (nome + controles) na direita */
.link-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    gap: 12px;
}

.link-details {
    flex: 1;
    min-width: 0;
}

.link-details h4 {
    color: var(--text-light);
    font-size: var(--mobile-font-size-large);
    font-weight: 600;
    margin-bottom: 0;
    line-height: 1.3;
    /* Mobile: Permitir quebra natural do texto, sem truncation */
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    word-wrap: break-word;
}

/* URL removida completamente do layout */
.link-details small {
    display: none;
}

/* Controles: No lugar do texto "Clique para acessar" */
.link-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    min-height: var(--touch-target-min);
}

/* Separador removido para simplificar layout */

/* Duplicação removida - estilo integrado acima */

/* Mobile: Melhor identificação visual dos botões */
.link-controls .control-btn {
    position: relative;
}

/* Toggle Switch - Mobile Optimized */
.toggle-switch {
    position: relative;
    /* Mobile: Tamanho otimizado para touch */
    width: 52px;
    height: 28px;
    background: #333;
    border-radius: 28px;
    cursor: pointer;
    transition: var(--transition);
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.toggle-switch.active {
    background: var(--accent-color);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    /* Mobile: Tamanho ajustado */
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
}

.toggle-switch.active::after {
    /* Mobile: Distância ajustada */
    transform: translateX(24px);
}

/* Botões de Controle - Mobile Optimized */
.control-btn {
    background: none;
    border: 1px solid var(--text-gray);
    color: var(--text-gray);
    /* Mobile: Touch target adequado */
    width: var(--touch-target-min);
    height: var(--touch-target-min);
    min-width: var(--touch-target-min);
    min-height: var(--touch-target-min);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    /* Touch optimization */
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.control-btn:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.control-btn.danger:hover {
    border-color: #e74c3c;
    color: #e74c3c;
}

.control-btn.edit:hover {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* Botões desabilitados */
.control-btn.disabled,
.control-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    pointer-events: none;
    border-color: var(--text-gray);
    color: var(--text-gray);
}

.control-btn.disabled:hover,
.control-btn:disabled:hover {
    border-color: var(--text-gray);
    color: var(--text-gray);
    transform: none;
}

/* ===== BREAKPOINTS PARA CARDS DO MODAL ===== */

/* Tablet (768px+): Restaurar layout horizontal */
@media (min-width: 768px) {
    .link-item {
        padding: 24px;
        border-radius: 16px;
    }

    /* Tablet: Layout horizontal com gaps maiores */
    .link-item-header {
        gap: 20px;
    }

    /* Tablet: Ícone ligeiramente maior */
    .link-preview {
        width: 48px;
        height: 48px;
        font-size: 1.4rem;
        border-radius: 14px;
    }

    .link-preview::before {
        border-radius: 14px;
    }

    /* Tablet: Gap maior entre nome e controles */
    .link-info {
        gap: 10px;
    }

    .link-details h4 {
        font-size: 1.1rem;
        margin-bottom: 4px;
        /* Tablet: Aplicar text truncation novamente */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Tablet: URL permanece oculta */
    .link-details small {
        display: none;
    }

    /* Tablet: Layout horizontal dos controles */
    .link-controls {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12px;
        width: auto;
        flex-shrink: 0;
    }

    /* Tablet: Layout já otimizado com flexbox */

    .control-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        min-height: 36px;
        font-size: 0.95rem;
    }
}

/* Desktop (1024px+): Layout otimizado */
@media (min-width: 1024px) {
    .link-item {
        padding: 28px;
    }

    /* Desktop: Layout horizontal com máximo espaçamento */
    .link-item-header {
        gap: 24px;
    }

    /* Desktop: Ícone com tamanho otimizado */
    .link-preview {
        width: 52px;
        height: 52px;
        font-size: 1.5rem;
        border-radius: 16px;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
    }

    .link-preview::before {
        border-radius: 16px;
    }

    /* Desktop: Gap máximo entre nome e controles */
    .link-info {
        gap: 12px;
    }

    .link-details h4 {
        font-size: 1.2rem;
    }

    /* Desktop: URL permanece oculta */
    .link-details small {
        display: none;
    }

    .link-controls {
        gap: 16px;
    }

    .control-btn {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
        font-size: 1rem;
    }
}

/* Botão Expandir Formulário com Shimmer Effect */
.btn-expand-form {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 15px 25px;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    width: 100%;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

/* Shimmer effect */
.btn-expand-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    background-size: 200% 100%;
    background-position: -200% 0;
    animation: shimmer 3s ease-in-out infinite;
}

.btn-expand-form:hover::before {
    background-position: 200% 0;
}

.btn-expand-form:hover {
    background: #f1c40f;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.btn-expand-form.hidden {
    display: none;
}

/* Container do Formulário Expansível */
.add-link-form-container {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    /* Estabilizar layout durante transição */
    will-change: max-height;
    contain: layout style;
    /* Prevenir overflow horizontal */
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
}

.add-link-form-container.expanded {
    max-height: 600px;
    /* Garantir altura fixa quando expandido para evitar recálculos */
    min-height: 400px;
    /* Estabilizar largura */
    width: 100%;
}

/* Formulários */
.add-link-form {
    background: var(--secondary-color);
    padding: 25px;
    border-radius: 10px;
    border: 1px solid #333;
    margin-top: 0;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-out 0.1s;
}

.add-link-form-container.expanded .add-link-form {
    opacity: 1;
    transform: translateY(0);
}

.form-group {
    margin-bottom: 20px;
    position: relative; /* Adicionado para contexto de empilhamento */
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 120px;
    gap: 15px;
}

.form-group label {
    display: block;
    color: var(--text-light);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

/* Inputs com Shine Border Effect */
.form-group input:not(.url-input-group input) {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.form-group input:not(.url-input-group input):focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow:
        0 0 0 2px rgba(212, 175, 55, 0.2),
        0 0 20px rgba(212, 175, 55, 0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    transform: translateY(-1px);
}

/* Shine effect para inputs */
.form-group input:not(.url-input-group input):focus::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(212, 175, 55, 0.2),
        transparent
    );
    background-size: 200% 100%;
    background-position: -200% 0;
    animation: shine 1.5s ease-in-out;
}

@keyframes shine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.form-group small {
    color: var(--text-gray);
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
}

/* URL Input Group - Interface composta para protocolo + URL */
.url-input-group {
    display: flex !important;
    align-items: stretch !important;
    width: 100% !important;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #333;
    background: var(--primary-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    height: auto;
}

.url-input-group:focus-within {
    border-color: var(--accent-color);
    box-shadow:
        0 0 0 2px rgba(212, 175, 55, 0.2),
        0 0 20px rgba(212, 175, 55, 0.1);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    transform: translateY(-1px);
}

.protocol-selector {
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-right: 1px solid #333;
    color: var(--text-light);
    font-size: 0.9rem;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 85px;
    flex-shrink: 0;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 6px center;
    background-size: 12px;
    padding-right: 24px;
}

.protocol-selector:focus {
    outline: none;
    background-color: rgba(212, 175, 55, 0.1);
}

.protocol-selector option {
    background: var(--primary-color);
    color: var(--text-light);
    padding: 8px;
}

.url-input-group input {
    flex: 1 !important;
    border: none !important;
    background: transparent !important;
    padding: 12px 15px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0 !important; /* Força o flex: 1 a funcionar corretamente */
    min-width: 0 !important; /* Permite que o input encolha se necessário */
    max-width: none !important;
}

.url-input-group input:focus {
    outline: none !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    transform: none !important;
}



.form-group select:not(.protocol-selector) {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.form-group select:not(.protocol-selector):focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-group select option {
    background: var(--primary-color);
    color: var(--text-light);
    padding: 10px;
}

/* Seletor de Ícones */
.icon-selector {
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative; /* Adicionado para contexto de empilhamento */
}

.icon-selector select {
    flex: 1;
}

.icon-preview {
    width: 45px;
    height: 45px;
    background: var(--secondary-color);
    border: 1px solid #333;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--text-gray);
    transition: var(--transition);
}

.icon-preview.active {
    color: var(--text-light);
    border-color: var(--accent-color);
    background: var(--primary-color);
}

/* Ações do Formulário */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-cancel-form {
    background: transparent;
    color: var(--text-gray);
    border: 1px solid var(--text-gray);
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-cancel-form:hover {
    color: var(--text-light);
    border-color: var(--text-light);
}

/* Botões */
.btn-add-link {
    background: var(--accent-color);
    color: var(--primary-color);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.btn-add-link:hover {
    background: #f1c40f;
    transform: translateY(-2px);
}

/* Footer do Modal */
.config-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25px 30px;
    border-top: 1px solid var(--secondary-color);
    background: #1a1a1a;
}

.footer-actions {
    display: flex;
    gap: 15px;
}

/* Botões com Magic UI Effects */
.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    border: none;
    position: relative;
    overflow: hidden;
}

/* Shimmer Button Effect para botões primários */
.btn-primary {
    background: var(--accent-color);
    color: var(--primary-color);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    background-size: 200% 100%;
    background-position: -200% 0;
    animation: shimmer 3s ease-in-out infinite;
}

.btn-primary:hover::before {
    background-position: 200% 0;
}

.btn-primary:hover {
    background: #f1c40f;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--text-gray);
    border: 1px solid var(--text-gray);
}

.btn-secondary:hover {
    color: var(--text-light);
    border-color: var(--text-light);
}

/* Animações Auxiliares */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(50px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Classes Utilitárias Responsivas */
.mobile-only { display: none; }
.desktop-only { display: block; }

@media (max-width: 767px) {
    .mobile-only { display: block; }
    .desktop-only { display: none; }
}

/* Modal Fullscreen para Mobile */
@media (max-width: 767px) {
    .config-modal {
        padding: 0;
        align-items: stretch;
        overflow: hidden;
    }

    .config-modal-content {
        width: 100vw;
        height: 100vh;
        max-width: none;
        max-height: none;
        margin: 0;
        border-radius: 0;
        animation: mobileSlideUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        flex-direction: column;
        overflow: hidden; /* CORREÇÃO MOBILE: Manter apenas overflow hidden - sem scroll no container principal */
        box-sizing: border-box;
    }

    /* Animação mobile otimizada */
    @keyframes mobileSlideUp {
        0% {
            transform: translateY(100%);
            opacity: 0.8;
        }
        100% {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Indicador de swipe */
    .swipe-indicator {
        display: flex;
        justify-content: center;
        padding: 12px 0 8px;
        background: var(--secondary-color);
        border-radius: 0;
    }

    .swipe-handle {
        width: 40px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .swipe-handle:active {
        background: var(--accent-color);
        width: 60px;
    }

    /* Header mobile */
    .config-header {
        padding: 16px 20px;
        border-radius: 0;
        flex-shrink: 0;
    }

    .config-header h2 {
        font-size: 1.3rem;
    }

    /* Body com scroll otimizado */
    .config-body {
        flex: 1;
        padding: 0;
        overflow: hidden; /* CORREÇÃO: Remover scroll do body em mobile */
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        /* Estabilizar altura em mobile */
        min-height: calc(100vh - 200px);
        contain: layout style;
        width: 100%;
        /* Estrutura flexível */
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
    }

    /* Footer fixo no bottom */
    .config-footer {
        padding: 16px 20px;
        border-radius: 0;
        flex-shrink: 0;
        background: var(--primary-color);
        border-top: 1px solid rgba(212, 175, 55, 0.2);
        flex-direction: column;
        gap: 12px;
    }

    .footer-actions {
        width: 100%;
        gap: 12px;
    }

    .footer-actions button {
        flex: 1;
        min-height: 48px;
        font-size: 1rem;
    }
}

/* ===== NAVEGAÇÃO POR ABAS ===== */

.config-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    /* Estabilizar layout das abas */
    contain: layout style;
    min-height: 400px;
}

/* Navegação das abas */
.tab-navigation {
    display: flex; /* Visível em todos os breakpoints */
    background: var(--secondary-color);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    padding: 0;
    flex-shrink: 0;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-gray);
    padding: 16px 8px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    position: relative;
    min-height: 64px;
}

.tab-btn i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.tab-btn span {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tab-btn.active {
    color: var(--accent-color);
    background: rgba(212, 175, 55, 0.1);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--accent-color);
    border-radius: 3px 3px 0 0;
}

.tab-btn:not(.active):hover {
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.05);
}

/* Conteúdo das abas */
.tab-content {
    flex: 1;
    overflow: visible;
    position: relative;
    /* Altura mínima para estabilizar layout */
    min-height: 350px;
    contain: layout style;
    /* Estrutura flexível para controle de altura */
    display: flex;
    flex-direction: column;
}

.tab-panel {
    display: none;
    flex: 1; /* CORREÇÃO: Usar flex para ocupar espaço disponível */
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    padding: 20px;
    /* Estabilizar scroll dos painéis */
    scroll-behavior: smooth;
    contain: layout style;
    width: 100%;
    box-sizing: border-box;
}

.tab-panel.active {
    display: block;
    animation: tabFadeIn 0.3s ease-out;
    /* Otimizar performance da animação */
    will-change: opacity;
    contain: layout style;
}

@keyframes tabFadeIn {
    0% {
        opacity: 0;
        /* Remover transform para evitar reflows */
    }
    100% {
        opacity: 1;
    }
}

/* Mobile: Configurações específicas para navegação por abas */
@media (max-width: 767px) {
    .tab-navigation {
        display: flex;
    }

    .tab-panel {
        padding: 16px;
        /* CORREÇÃO MOBILE: Garantir que apenas tab-panel tenha scroll */
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        /* Altura específica para mobile */
        height: calc(100vh - 200px);
        max-height: calc(100vh - 200px);
    }

    .section-title {
        margin-bottom: 16px;
    }
}

/* ===== OTIMIZAÇÕES TOUCH ===== */

/* Botões otimizados para touch */
@media (max-width: 767px) {
    .btn-expand-form,
    .btn-primary,
    .btn-secondary,
    .btn-add-link,
    .btn-cancel-form {
        min-height: 48px;
        padding: 14px 20px;
        font-size: 1rem;
        border-radius: 12px;
        touch-action: manipulation;
    }

    /* Controles dos links */
    .control-btn {
        min-width: 44px;
        min-height: 44px;
        font-size: 1.1rem;
        border-radius: 8px;
    }

    /* Toggle switches maiores */
    .toggle-switch {
        width: 60px;
        height: 32px;
        border-radius: 32px;
    }

    .toggle-switch::after {
        width: 28px;
        height: 28px;
        top: 2px;
        left: 2px;
    }

    .toggle-switch.active::after {
        transform: translateX(28px);
    }

    /* Inputs otimizados */
    .form-group input:not(.url-input-group input),
    .form-group select:not(.protocol-selector) {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
        border-radius: 12px;
    }

    /* URL Input Group - Mobile otimizado */
    .url-input-group {
        min-height: 48px;
        border-radius: 12px;
    }

    .protocol-selector {
        min-height: 48px;
        padding: 14px 8px;
        font-size: 1rem;
        width: 95px;
        padding-right: 28px;
        background-size: 14px;
        background-position: right 8px center;
    }

    .url-input-group input {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
    }

    /* Cards de links com melhor espaçamento */
    .link-item {
        padding: 20px 16px;
        margin-bottom: 12px;
        border-radius: 16px;
    }

    .link-item-header {
        gap: 22px;
    }

    /* Telas médias: Ícone com tamanho intermediário */
    .link-preview {
        width: 50px;
        height: 50px;
        font-size: 1.45rem;
        border-radius: 15px;
    }

    .link-preview::before {
        border-radius: 15px;
    }

    /* Telas médias: Gap intermediário entre nome e controles */
    .link-info {
        gap: 11px;
    }

    .link-controls {
        gap: 12px;
        flex-wrap: wrap;
    }

    /* Preview de ícones maior */
    .icon-preview {
        width: 48px;
        height: 48px;
        font-size: 1.4rem;
    }
}

/* ===== ABA DE CONFIGURAÇÕES ===== */

.settings-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.setting-item {
    background: var(--secondary-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
}

.setting-item:hover {
    border-color: rgba(212, 175, 55, 0.3);
    transform: translateY(-1px);
}

.setting-info h4 {
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px 0;
}

.setting-info small {
    color: var(--text-gray);
    font-size: 0.85rem;
    line-height: 1.4;
}

.setting-control {
    flex-shrink: 0;
}

.form-select {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-select option {
    background: var(--primary-color);
    color: var(--text-light);
}

.form-input {
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    padding: 8px 12px;
    font-size: 0.9rem;
    min-width: 120px;
    transition: all 0.3s ease;
    width: 100%;
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.form-input::placeholder {
    color: var(--text-gray);
    opacity: 0.7;
}

/* Mobile: Configurações em coluna */
@media (max-width: 767px) {
    .setting-item {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 12px;
    }

    .setting-control {
        align-self: center;
    }

    .form-select {
        min-width: 140px;
        min-height: 44px;
        font-size: 1rem;
    }

    .form-input {
        min-height: 44px;
        font-size: 1rem;
        padding: 12px 16px;
    }
}

/* ===== BREAKPOINTS ESPECÍFICOS ===== */

/* Mobile Small (320px - 374px) */
@media (max-width: 374px) {
    .config-button {
        top: 12px;
        right: 12px;
        width: 44px; /* Mínimo de 44px para acessibilidade em dispositivos móveis */
        height: 44px; /* Mínimo de 44px para acessibilidade em dispositivos móveis */
        font-size: 1rem;
    }

    .config-header {
        padding: 12px 16px;
    }

    .config-header h2 {
        font-size: 1.2rem;
    }

    .tab-btn {
        padding: 12px 4px;
        min-height: 56px;
    }

    .tab-btn i {
        font-size: 1.1rem;
    }

    .tab-btn span {
        font-size: 0.7rem;
    }

    .tab-panel {
        padding: 12px;
    }

    .link-item {
        padding: 16px 12px;
    }

    .link-item-header {
        gap: 18px;
    }

    /* Telas pequenas: Manter ícone com destaque */
    .link-preview {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
    }

    /* Telas pequenas: Gap mínimo entre nome e controles */
    .link-info {
        gap: 9px;
    }

    .link-controls {
        align-self: stretch;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 8px;
    }

    .control-btn {
        min-width: 40px;
        min-height: 40px;
        font-size: 1rem;
    }

    .setting-item {
        padding: 16px 12px;
    }

    .footer-actions {
        flex-direction: column;
        gap: 8px;
    }

    .footer-actions button {
        min-height: 44px;
    }
}

/* Mobile Medium (375px - 767px) */
@media (min-width: 375px) and (max-width: 767px) {
    .config-header h2 {
        font-size: 1.4rem;
    }

    .tab-panel {
        padding: 18px;
    }

    .link-item {
        padding: 18px 16px;
    }

    .setting-item {
        padding: 18px 16px;
    }
}

/* Tablet (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .config-modal-content {
        max-width: 700px;
        margin-top: 40px;
    }

    .config-header {
        padding: 24px 28px;
    }

    .config-body {
        padding: 24px 28px;
    }

    .config-footer {
        padding: 24px 28px;
    }

    .tab-navigation {
        display: flex; /* Mostrar navegação por abas em tablet também */
    }

    .tab-panel {
        display: none; /* Usar sistema de abas em tablet */
        padding: 20px;
        flex: 1; /* CORREÇÃO: Usar flex em tablet */
        overflow-y: auto; /* MANTER: Scroll apenas no painel ativo */
    }

    .tab-panel.active {
        display: block; /* Mostrar apenas aba ativa em tablet */
    }

    .section-title {
        display: block; /* Mostrar títulos das seções em tablet */
    }
}

/* Desktop Large (1024px+) */
@media (min-width: 1024px) {
    .config-modal-content {
        max-width: 800px;
    }

    .config-header {
        padding: 28px 32px;
    }

    .config-body {
        padding: 28px 32px;
    }

    .config-footer {
        padding: 28px 32px;
    }
}

/* ===== VALIDAÇÃO EM TEMPO REAL ===== */

.form-group input.valid {
    border-color: #27ae60 !important;
    box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
}

.form-group input.invalid {
    border-color: #e74c3c !important;
    box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
}

.form-group input.valid::after {
    content: '✓';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #27ae60;
    font-weight: bold;
}

/* ===== CAMPOS ESPECÍFICOS DO WHATSAPP ===== */

.whatsapp-fields {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(37, 211, 102, 0.05) 0%, rgba(37, 211, 102, 0.02) 100%);
    border: 1px solid rgba(37, 211, 102, 0.2);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: slideInUp 0.3s ease-out;
}

.whatsapp-fields::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #25d366, #128c7e);
    opacity: 0.8;
}

.whatsapp-fields .form-group {
    margin-bottom: 16px;
}

.whatsapp-fields .form-group:last-child {
    margin-bottom: 0;
}

.whatsapp-fields label {
    color: #25d366;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.whatsapp-fields label::before {
    content: '📱';
    font-size: 0.9rem;
}

.whatsapp-fields input[type="tel"] {
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
}

.whatsapp-fields textarea {
    width: 100%;
    padding: 12px 15px;
    background: var(--primary-color);
    border: 1px solid #333;
    border-radius: 8px;
    color: var(--text-light);
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-fields textarea:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 2px rgba(37, 211, 102, 0.2);
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
}

.field-hint {
    color: var(--text-gray);
    font-size: 0.75rem;
    margin-top: 6px;
    display: block;
    font-style: italic;
    opacity: 0.8;
}

.character-counter {
    color: var(--text-gray);
    font-size: 0.75rem;
    margin-top: 4px;
    display: block;
    text-align: right;
    font-family: 'Courier New', monospace;
}

/* Animação de entrada para campos do WhatsApp já incluída no seletor principal */

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade para campos do WhatsApp */
@media (max-width: 767px) {
    .whatsapp-fields {
        padding: 16px;
        margin-top: 16px;
    }

    .whatsapp-fields .form-group {
        margin-bottom: 14px;
    }

    .whatsapp-fields input[type="tel"],
    .whatsapp-fields textarea {
        min-height: 48px;
        padding: 14px 16px;
        font-size: 1rem;
        border-radius: 12px;
    }

    .whatsapp-fields textarea {
        min-height: 100px;
    }
}

/* ===== OTIMIZAÇÕES DE PERFORMANCE ===== */

/* Estabilização de scroll - propriedades já adicionadas aos seletores existentes */

/* Reduz efeitos em dispositivos móveis */
@media (max-width: 767px) {
    .config-modal-content[style*="--reduce-effects"] {
        backdrop-filter: blur(5px) !important;
    }

    .config-modal-content[style*="--reduce-effects"]::before,
    .config-modal-content[style*="--reduce-effects"]::after {
        display: none !important;
    }

    .link-item::before,
    .link-item::after {
        display: none !important;
    }

    .floating-particle {
        display: none !important;
    }
}

/* Classe duplicada removida - definição principal está nas otimizações de performance */

/* ===== OTIMIZAÇÕES TOUCH ===== */

.touch-device button,
.touch-device .link-item,
.touch-device .toggle-switch {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.touch-device button:active {
    transform: scale(0.98);
}

/* Melhora áreas de toque */
@media (max-width: 767px) {
    .control-btn {
        position: relative;
    }

    .control-btn::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        background: transparent;
    }
}

.choices-custom .choices__list--dropdown {
    background-color: var(--secondary-color);
    border-color: var(--accent-color);
}

.choices-custom .choices__item {
    color: var(--text-light);
}

.choices-custom .choices__item--choice {
    background-color: var(--secondary-color);
}

.choices-custom .choices__item--choice.is-highlighted {
    background-color: var(--accent-color);
    color: var(--primary-color);
}

/* ===== ACESSIBILIDADE ===== */

/* Foco visível melhorado */
.tab-btn:focus,
button:focus,
input:focus,
select:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Navegação por teclado */
.tab-btn:focus-visible {
    background: rgba(212, 175, 55, 0.2);
}

/* Screen reader */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== ANIMAÇÕES MOBILE-FRIENDLY ===== */

/* Animação de entrada otimizada para mobile */
@media (max-width: 767px) {
    .config-modal.active {
        animation: mobileModalFadeIn 0.3s ease-out;
    }

    @keyframes mobileModalFadeIn {
        0% {
            opacity: 0;
            backdrop-filter: blur(0px);
        }
        100% {
            opacity: 1;
            backdrop-filter: blur(5px);
        }
    }

    /* Animação de saída */
    .config-modal.closing {
        animation: mobileModalFadeOut 0.3s ease-out;
    }

    @keyframes mobileModalFadeOut {
        0% {
            opacity: 1;
            backdrop-filter: blur(5px);
        }
        100% {
            opacity: 0;
            backdrop-filter: blur(0px);
        }
    }

    .config-modal-content.closing {
        animation: mobileSlideDown 0.3s ease-out;
    }

    @keyframes mobileSlideDown {
        0% {
            transform: translateY(0);
            opacity: 1;
        }
        100% {
            transform: translateY(100%);
            opacity: 0.8;
        }
    }
}

/* ===== MELHORIAS DE CONTRASTE ===== */

@media (prefers-contrast: high) {
    .tab-btn {
        border: 1px solid var(--text-gray);
    }

    .tab-btn.active {
        border-color: var(--accent-color);
        background: var(--accent-color);
        color: var(--primary-color);
    }

    .link-item {
        border: 1px solid var(--text-gray);
    }
}

/* ===== MODO ESCURO/CLARO ===== */

@media (prefers-color-scheme: light) {
    .config-modal[data-theme="auto"] {
        --primary-color: #ffffff;
        --secondary-color: #f5f5f5;
        --text-light: #333333;
        --text-gray: #666666;
    }
}

/* ===== MODAL DE EDIÇÃO ===== */

/* Modal de Edição */
.edit-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 9999; /* Garante que o modal de edição apareça acima de tudo */
    overflow: visible; /* FIX: Permite que o dropdown do Choices.js apareça */
    padding: 20px;
}

.edit-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

/* Edit Modal com Magic UI */
.edit-modal-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 500px;
    max-height: calc(100vh - 40px); /* ✅ CORREÇÃO: Altura máxima para evitar overflow */
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(212, 175, 55, 0.1);
    animation: magicModalSlideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid transparent;
    background-clip: padding-box;
    display: flex; /* ✅ CORREÇÃO: Estrutura flexível */
    flex-direction: column; /* ✅ CORREÇÃO: Layout vertical */
    overflow: hidden; /* ✅ CORREÇÃO: Sem scroll no container principal */
}

/* Neon gradient border effect para edit modal */
.edit-modal-content::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg,
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent,
        rgba(212, 175, 55, 0.3),
        transparent
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: neonBorderRotate 3s linear infinite;
}

/* Spotlight effect para edit modal */
.edit-modal-content::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: radial-gradient(
        400px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
        rgba(212, 175, 55, 0.1),
        transparent 40%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.edit-modal-content:hover::after {
    opacity: 1;
}

/* Header do Modal de Edição com Magic UI */
.edit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
    background: linear-gradient(135deg, var(--secondary-color), #1f1f1f);
    position: relative;
    overflow: hidden;
}

/* Animated gradient text para o título do edit modal */
.edit-header h3 {
    background: linear-gradient(
        45deg,
        var(--accent-color),
        #f1c40f,
        var(--accent-color),
        #ffd700
    );
    background-size: 300% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

/* Shimmer effect para o ícone do título do edit modal */
.edit-header h3 i {
    background: var(--accent-color);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: shimmer 2s ease-in-out infinite;
}

.edit-close {
    background: none;
    border: none;
    color: var(--text-gray);
    font-size: 1.3rem;
    cursor: pointer;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-close:hover {
    color: var(--text-light);
    background: rgba(255, 255, 255, 0.1);
}

/* Corpo do Modal de Edição */
.edit-body {
    padding: 25px;
    flex: 1; /* ✅ CORREÇÃO: Ocupar espaço disponível */
    overflow-y: auto; /* ✅ CORREÇÃO: Scroll apenas no corpo quando necessário */
    overflow-x: hidden; /* ✅ CORREÇÃO: Sem scroll horizontal */
}

.edit-link-form {
    background: var(--secondary-color);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #333;
}

/* Ações do Modal de Edição */
.edit-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Responsividade do Modal de Edição */
@media (max-width: 768px) {
    .edit-modal {
        padding: 10px;
        overflow: hidden; /* ✅ CORREÇÃO: Garantir sem scroll no container mobile */
    }

    .edit-modal-content {
        max-width: 100%;
        max-height: calc(100vh - 20px); /* ✅ CORREÇÃO: Altura máxima ajustada para mobile */
    }

    .edit-header {
        padding: 15px 20px;
        flex-shrink: 0; /* ✅ CORREÇÃO: Header não deve encolher */
    }

    .edit-body {
        padding: 20px;
        flex: 1; /* ✅ CORREÇÃO: Corpo ocupa espaço disponível */
        overflow-y: auto; /* ✅ CORREÇÃO: Scroll apenas no corpo */
        overflow-x: hidden; /* ✅ CORREÇÃO: Sem scroll horizontal */
    }

    .edit-actions {
        flex-direction: column;
        flex-shrink: 0; /* ✅ CORREÇÃO: Ações não devem encolher */
    }
}

/* ===== MAGIC UI EFFECTS ===== */

/* Partículas flutuantes */
.floating-particle {
    position: fixed !important;
    width: 4px !important;
    height: 4px !important;
    background: var(--accent-color) !important;
    border-radius: 50% !important;
    pointer-events: none !important;
    z-index: 1 !important;
    opacity: 0.6 !important;
}

/* Efeito de typing cursor para o título */
.title.typing::after {
    content: '|';
    animation: blink 1s infinite;
    color: var(--accent-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Novas animações Magic UI para o modal */
@keyframes magicModalFadeIn {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(10px);
    }
}

@keyframes magicModalSlideUp {
    0% {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0px);
    }
}

@keyframes neonBorderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}



/* Responsividade para efeitos */
@media (max-width: 768px) {
    .title::before,
    .title::after {
        display: none;
    }

    .floating-particle {
        display: none;
    }

    .link-button:hover {
        transform: translateY(-1px) scale(1.01);
    }
}
