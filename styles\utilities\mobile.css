/* ===== OTIMIZAÇÕES MOBILE ===== */

/* Classes Utilitárias Responsivas */
.mobile-only { display: none; }
.desktop-only { display: block; }

@media (max-width: 767px) {
    .mobile-only { display: block; }
    .desktop-only { display: none; }
}

/* Otimizações de Performance para Mobile */
.mobile-device .link-button::before,
.mobile-device .link-button::after,
.mobile-device .link-item::before,
.mobile-device .link-item::after,
.mobile-device .floating-particle {
    display: none !important;
}

.low-performance *,
.reduce-animations * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
    animation-iteration-count: 1 !important;
}

.low-performance .link-button:hover,
.low-performance .link-item:hover,
.reduce-animations .link-button:hover,
.reduce-animations .link-item:hover {
    transform: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.low-performance .config-modal-content {
    backdrop-filter: none !important;
    background: var(--primary-color) !important;
}

.reduce-animations .link-button::before,
.reduce-animations .link-button::after,
.reduce-animations .link-item::before,
.reduce-animations .link-item::after {
    animation: none !important;
}
