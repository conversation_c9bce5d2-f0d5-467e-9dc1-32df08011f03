/* ===== LINK TREE - MAIN STYLESHEET ===== */
/* Arquivo principal de importação CSS modular */
/* Arquitetura baseada em BEM e separação por responsabilidade */

/* ===== 1. BASE - FUNDAÇÃO ===== */
/* Reset, variáveis e configurações básicas */
@import './base/reset.css';
@import './base/variables.css';
@import './base/typography.css';

/* ===== 2. LAYOUT - ESTRUTURA ===== */
/* Container, grid e responsividade base */
@import './layout/container.css';
@import './layout/grid.css';
@import './layout/responsive.css';

/* ===== 3. EFFECTS - EFEITOS BASE ===== */
/* Animações, Magic UI e efeitos de hover */
@import './effects/animations.css';
@import './effects/magic-ui.css';
@import './effects/hover-effects.css';

/* ===== 4. COMPONENTS - COMPONENTES ===== */
/* Componentes modulares da interface */
@import './components/header.css';
@import './components/link-button.css';
@import './components/footer.css';
@import './components/modal.css';
@import './components/forms.css';
@import './components/tabs.css';
@import './components/settings.css';

/* ===== 5. UTILITIES - OTIMIZAÇÕES ===== */
/* Otimizações específicas e utilities */
@import './utilities/mobile.css';
@import './utilities/touch.css';
@import './utilities/performance.css';

/* ===== INFORMAÇÕES DA ARQUITETURA ===== */
/*
Estrutura modular seguindo as melhores práticas:

📁 base/
  - reset.css: Reset CSS e configurações do body
  - variables.css: Variáveis CSS customizadas
  - typography.css: Configurações de tipografia

📁 layout/
  - container.css: Container principal e estrutura
  - grid.css: Background grid pattern
  - responsive.css: Breakpoints e media queries

📁 effects/
  - animations.css: Keyframes e animações
  - magic-ui.css: Efeitos Magic UI (shimmer, aurora, etc.)
  - hover-effects.css: Efeitos de hover e interação

📁 components/
  - header.css: Logo, título e subtítulo
  - link-button.css: Botões de link principais
  - footer.css: Footer e ícones sociais
  - modal.css: Sistema de modal de configurações
  - forms.css: Formulários expansíveis
  - tabs.css: Navegação por abas
  - settings.css: Configurações específicas

📁 utilities/
  - mobile.css: Otimizações mobile
  - touch.css: Otimizações touch
  - performance.css: Otimizações de performance

Benefícios desta arquitetura:
✅ Melhor manutenibilidade
✅ Facilidade para encontrar estilos específicos
✅ Possibilidade de carregar apenas módulos necessários
✅ Melhor organização do código
✅ Facilita trabalho em equipe
✅ Segue metodologia BEM
✅ Mantém especificidade CSS adequada
✅ Preserva funcionalidade mobile-first
✅ Mantém todos os efeitos Magic UI
*/
