<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Estilos Modulares</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <i class="fas fa-link"></i>
            </div>
            <h1 class="title">Teste de Estilos</h1>
            <p class="subtitle">Verificando se a refatoração CSS funcionou</p>
        </header>

        <section class="links-section">
            <a href="#" class="link-button whatsapp">
                <div class="button-content">
                    <i class="fab fa-whatsapp"></i>
                    <div class="button-text">
                        <strong>WhatsApp</strong>
                        <small>Teste de link</small>
                    </div>
                </div>
                <i class="fas fa-arrow-right arrow"></i>
            </a>

            <a href="#" class="link-button instagram">
                <div class="button-content">
                    <i class="fab fa-instagram"></i>
                    <div class="button-text">
                        <strong>Instagram</strong>
                        <small>Teste de link</small>
                    </div>
                </div>
                <i class="fas fa-arrow-right arrow"></i>
            </a>
        </section>

        <footer class="footer">
            <p>Teste da arquitetura CSS modular</p>
            <div class="social-icons">
                <i class="fab fa-github"></i>
                <i class="fab fa-linkedin"></i>
            </div>
        </footer>
    </div>

    <!-- Botão de configurações -->
    <button class="config-button">
        <i class="fas fa-cog"></i>
    </button>

    <script>
        // Teste simples para verificar se os estilos estão carregando
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const computedStyle = window.getComputedStyle(container);
            
            console.log('=== TESTE DE ESTILOS MODULARES ===');
            console.log('Container max-width:', computedStyle.maxWidth);
            console.log('Container display:', computedStyle.display);
            console.log('Container gap:', computedStyle.gap);
            
            const linkButton = document.querySelector('.link-button');
            if (linkButton) {
                const buttonStyle = window.getComputedStyle(linkButton);
                console.log('Link button background:', buttonStyle.background);
                console.log('Link button border-radius:', buttonStyle.borderRadius);
            }
            
            console.log('✅ Estilos carregados com sucesso!');
        });
    </script>
</body>
</html>
